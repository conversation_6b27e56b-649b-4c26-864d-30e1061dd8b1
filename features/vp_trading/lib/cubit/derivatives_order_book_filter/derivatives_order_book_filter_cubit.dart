import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

part 'derivatives_order_book_filter_state.dart';

/// Cubit for managing derivatives order book filter state
/// Implements dual cubit pattern following VP Utility Module standards
/// Maintains independent filter state for Regular Orders and Conditional Orders tabs
class DerivativesOrderBookFilterCubit
    extends Cubit<DerivativesOrderBookFilterState> {
  DerivativesOrderBookFilterCubit()
    : super(const DerivativesOrderBookFilterState());

  /// Initialize with default filter values
  void init() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
      ),
    );
  }

  /// Change current tab index
  void changeTab(int tabIndex) {
    emit(state.copyWith(currentTabIndex: tabIndex));
  }

  /// Update filter for Regular Orders tab
  void updateRegularOrderFilter(DerivativesFilterParam filterParam) {
    emit(state.copyWith(regularOrderFilter: filterParam));
  }

  /// Update filter for Conditional Orders tab
  void updateConditionalOrderFilter(DerivativesFilterParam filterParam) {
    emit(state.copyWith(conditionalOrderFilter: filterParam));
  }

  /// Update filter for current active tab
  void updateCurrentFilter(DerivativesFilterParam filterParam) {
    switch (state.currentTabIndex) {
      case 0:
        updateRegularOrderFilter(filterParam);
        break;
      case 1:
        updateConditionalOrderFilter(filterParam);
        break;
    }
  }

  /// Reset filter for current active tab to default
  void resetCurrentFilter() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );
    updateCurrentFilter(defaultFilter);
  }

  /// Reset all filters to default
  void resetAllFilters() {
    const defaultFilter = DerivativesFilterParam(
      transactionType: OrderTypeEnum.all,
      orderStatus: [OrderStatusEnum.all],
    );

    emit(
      state.copyWith(
        regularOrderFilter: defaultFilter,
        conditionalOrderFilter: defaultFilter,
      ),
    );
  }

 
}
