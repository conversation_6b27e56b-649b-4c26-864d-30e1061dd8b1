import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

/// Filter parameters for derivatives order book
class DerivativesFilterParam {
  final OrderTypeEnum? transactionType;
  final List<OrderStatusEnum>? orderStatus;

  const DerivativesFilterParam({this.transactionType, this.orderStatus});

  DerivativesFilterParam copyWith({
    OrderTypeEnum? transactionType,
    List<OrderStatusEnum>? orderStatus,
  }) {
    return DerivativesFilterParam(
      transactionType: transactionType ?? this.transactionType,
      orderStatus: orderStatus ?? this.orderStatus,
    );
  }

  /// Check if filter is in default state (All selected for both criteria)
  bool get isDefault {
    final isTransactionTypeDefault =
        transactionType == null || transactionType == OrderTypeEnum.all;
    final isOrderStatusDefault =
        orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all);

    return isTransactionTypeDefault && isOrderStatusDefault;
  }

  /// Get the combined status codes for API request
  String get statusCodesForRequest {
    if (orderStatus == null ||
        orderStatus!.isEmpty ||
        orderStatus!.contains(OrderStatusEnum.all)) {
      return "";
    }

    final allCodes = <String>[];
    for (final status in orderStatus!) {
      if (status != OrderStatusEnum.all) {
        allCodes.addAll(status.codes);
      }
    }

    return allCodes.join(',');
  }

  /// Get transaction type code for API request
  String get transactionTypeCodeForRequest {
    if (transactionType == null || transactionType == OrderTypeEnum.all) {
      return "";
    }
    return transactionType!.codeRequest;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is DerivativesFilterParam &&
        other.transactionType == transactionType &&
        _listEquals(other.orderStatus, orderStatus);
  }

  @override
  int get hashCode {
    final transactionTypeHash = transactionType?.hashCode ?? 0;
    final orderStatusHash =
        orderStatus?.map((e) => e.hashCode).fold(0, (a, b) => a ^ b) ?? 0;
    return transactionTypeHash ^ orderStatusHash;
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
