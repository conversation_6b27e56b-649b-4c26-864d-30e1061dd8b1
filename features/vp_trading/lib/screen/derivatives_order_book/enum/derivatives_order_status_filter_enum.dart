import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

/// Enhanced order status enum for derivatives order book filter
/// Maps to the detailed order statuses as specified in requirements
enum DerivativesOrderStatusFilterEnum {
  all,
  pendingSend,
  pendingMatch,
  partiallyFilled,
  fullyFilled,
  cancelled,
  modified,
  rejected,
  expired,
}

extension DerivativesOrderStatusFilterEnumExt on DerivativesOrderStatusFilterEnum {
  String get title {
    switch (this) {
      case DerivativesOrderStatusFilterEnum.all:
        return VPTradingLocalize.current.trading_all;
      case DerivativesOrderStatusFilterEnum.pendingSend:
        return 'Chờ Gửi';
      case DerivativesOrderStatusFilterEnum.pendingMatch:
        return 'Chờ Khớp';
      case DerivativesOrderStatusFilterEnum.partiallyFilled:
        return 'Khớp 1 Phần';
      case DerivativesOrderStatusFilterEnum.fullyFilled:
        return '<PERSON>hớ<PERSON>ết';
      case DerivativesOrderStatusFilterEnum.cancelled:
        return 'Đã <PERSON>ủy';
      case DerivativesOrderStatusFilterEnum.modified:
        return 'Đã Sửa';
      case DerivativesOrderStatusFilterEnum.rejected:
        return 'Từ Chối';
      case DerivativesOrderStatusFilterEnum.expired:
        return 'Hết Hạn';
    }
  }

  Color get color {
    switch (this) {
      case DerivativesOrderStatusFilterEnum.fullyFilled:
        return vpColor.backgroundAccentGreen;
      case DerivativesOrderStatusFilterEnum.pendingSend:
      case DerivativesOrderStatusFilterEnum.pendingMatch:
        return vpColor.backgroundAccentNeutral2;
      case DerivativesOrderStatusFilterEnum.partiallyFilled:
        return vpColor.backgroundAccentBlue;
      case DerivativesOrderStatusFilterEnum.cancelled:
      case DerivativesOrderStatusFilterEnum.rejected:
      case DerivativesOrderStatusFilterEnum.expired:
        return vpColor.backgroundAccentRed;
      case DerivativesOrderStatusFilterEnum.modified:
        return vpColor.backgroundAccentOrange;
      case DerivativesOrderStatusFilterEnum.all:
        return Colors.black54;
    }
  }

  Color get textColor {
    switch (this) {
      case DerivativesOrderStatusFilterEnum.fullyFilled:
        return vpColor.textAccentGreen;
      case DerivativesOrderStatusFilterEnum.pendingSend:
      case DerivativesOrderStatusFilterEnum.pendingMatch:
        return vpColor.textAccentBlue;
      case DerivativesOrderStatusFilterEnum.partiallyFilled:
        return vpColor.textAccentBlue;
      case DerivativesOrderStatusFilterEnum.cancelled:
      case DerivativesOrderStatusFilterEnum.rejected:
      case DerivativesOrderStatusFilterEnum.expired:
        return vpColor.textAccentRed;
      case DerivativesOrderStatusFilterEnum.modified:
        return vpColor.textAccentOrange;
      case DerivativesOrderStatusFilterEnum.all:
        return Colors.black54;
    }
  }

  /// Maps to the system status codes used in API requests
  List<String> get codes {
    switch (this) {
      case DerivativesOrderStatusFilterEnum.pendingSend:
        return ["PS"];
      case DerivativesOrderStatusFilterEnum.pendingMatch:
        return ["ST", "WT", "PR", "PC", "WC", "WE", "OP", "WD", "WA"];
      case DerivativesOrderStatusFilterEnum.partiallyFilled:
        return ["PF"];
      case DerivativesOrderStatusFilterEnum.fullyFilled:
        return ["FF", "CP"];
      case DerivativesOrderStatusFilterEnum.cancelled:
        return ["CN"];
      case DerivativesOrderStatusFilterEnum.modified:
        return ["RP"];
      case DerivativesOrderStatusFilterEnum.rejected:
        return ["RJ"];
      case DerivativesOrderStatusFilterEnum.expired:
        return ["EX", "EP"];
      case DerivativesOrderStatusFilterEnum.all:
        return ["ALL"];
    }
  }

  /// Returns the comma-separated string for API requests
  String get codeRequest {
    switch (this) {
      case DerivativesOrderStatusFilterEnum.pendingSend:
        return "PS";
      case DerivativesOrderStatusFilterEnum.pendingMatch:
        return "ST,WT,PR,PC,WC,WE,OP,WD,WA";
      case DerivativesOrderStatusFilterEnum.partiallyFilled:
        return "PF";
      case DerivativesOrderStatusFilterEnum.fullyFilled:
        return "FF,CP";
      case DerivativesOrderStatusFilterEnum.cancelled:
        return "CN";
      case DerivativesOrderStatusFilterEnum.modified:
        return "RP";
      case DerivativesOrderStatusFilterEnum.rejected:
        return "RJ";
      case DerivativesOrderStatusFilterEnum.expired:
        return "EX,EP";
      case DerivativesOrderStatusFilterEnum.all:
        return "";
    }
  }

  /// Creates enum from system status code
  static DerivativesOrderStatusFilterEnum fromCode(String? code) {
    switch (code?.toUpperCase()) {
      case "PS":
        return DerivativesOrderStatusFilterEnum.pendingSend;
      case "ST":
      case "WT":
      case "PR":
      case "PC":
      case "WC":
      case "WE":
      case "OP":
      case "WD":
      case "WA":
        return DerivativesOrderStatusFilterEnum.pendingMatch;
      case "PF":
        return DerivativesOrderStatusFilterEnum.partiallyFilled;
      case "FF":
      case "CP":
        return DerivativesOrderStatusFilterEnum.fullyFilled;
      case "CN":
        return DerivativesOrderStatusFilterEnum.cancelled;
      case "RP":
        return DerivativesOrderStatusFilterEnum.modified;
      case "RJ":
        return DerivativesOrderStatusFilterEnum.rejected;
      case "EX":
      case "EP":
        return DerivativesOrderStatusFilterEnum.expired;
      case "ALL":
        return DerivativesOrderStatusFilterEnum.all;
      default:
        return DerivativesOrderStatusFilterEnum.all;
    }
  }

  /// Display name for multiple selected statuses
  static String displayNameFilter(List<DerivativesOrderStatusFilterEnum> listStatus) {
    if (listStatus.contains(DerivativesOrderStatusFilterEnum.all)) {
      return VPTradingLocalize.current.trading_status_all;
    } else {
      if (listStatus.length == 1) {
        return listStatus.first.title;
      }
      return "Đã chọn ${listStatus.length}";
    }
  }
}
