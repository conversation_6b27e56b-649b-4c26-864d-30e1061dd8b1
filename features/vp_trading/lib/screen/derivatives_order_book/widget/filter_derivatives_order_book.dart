import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/screen/derivatives_order_book/enum/derivatives_order_status_filter_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void openFilterDerivativesOrderBottomSheet(
  BuildContext context, {
  DerivativesFilterParam? initialFilter,
  Function(DerivativesFilterParam)? onApply,
}) {
  VPPopup.bottomSheet(
    _FilterDerivativesOrder(initialFilter: initialFilter, onApply: onApply),
  ).showSheet(context);
}

class _FilterDerivativesOrder extends StatefulWidget {
  const _FilterDerivativesOrder({super.key, this.initialFilter, this.onApply});

  final DerivativesFilterParam? initialFilter;
  final Function(DerivativesFilterParam)? onApply;

  @override
  State<_FilterDerivativesOrder> createState() =>
      __FilterDerivativesOrderState();
}

class __FilterDerivativesOrderState extends State<_FilterDerivativesOrder> {
  late OrderTypeEnum _selectedTransactionType;
  late List<DerivativesOrderStatusFilterEnum> _selectedOrderStatus;

  @override
  void initState() {
    super.initState();

    // Initialize transaction type
    _selectedTransactionType =
        widget.initialFilter?.transactionType ?? OrderTypeEnum.all;

    // Initialize order status selection
    if (widget.initialFilter?.orderStatus == null ||
        widget.initialFilter!.orderStatus!.isEmpty ||
        widget.initialFilter!.orderStatus!.contains(
          DerivativesOrderStatusFilterEnum.all,
        )) {
      _selectedOrderStatus = [DerivativesOrderStatusFilterEnum.all];
    } else {
      _selectedOrderStatus = List<DerivativesOrderStatusFilterEnum>.from(
        widget.initialFilter!.orderStatus!,
      );
    }
  }

  void _onSelectTransactionType(OrderTypeEnum transactionType) {
    setState(() {
      _selectedTransactionType = transactionType;
    });
  }

  bool get isSelectAllOrderStatus =>
      _selectedOrderStatus.contains(DerivativesOrderStatusFilterEnum.all) ||
      _selectedOrderStatus.length ==
          DerivativesOrderStatusFilterEnum.values.length;

  void _onTapAllOrderStatus() {
    setState(() {
      _selectedOrderStatus = [DerivativesOrderStatusFilterEnum.all];
    });
  }

  void _onTapOrderStatus(DerivativesOrderStatusFilterEnum status) {
    setState(() {
      if (_selectedOrderStatus.contains(DerivativesOrderStatusFilterEnum.all)) {
        // If "all" is selected, remove "all" and add all other statuses except the clicked one
        _selectedOrderStatus =
            DerivativesOrderStatusFilterEnum.values
                .where(
                  (e) =>
                      e != DerivativesOrderStatusFilterEnum.all && e != status,
                )
                .toList();
      } else {
        if (_selectedOrderStatus.contains(status)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedOrderStatus.length == 1 &&
              !_selectedOrderStatus.contains(
                DerivativesOrderStatusFilterEnum.all,
              )) {
            return; // Don't allow unchecking the last item
          }
          _selectedOrderStatus.remove(status);
        } else {
          _selectedOrderStatus.add(status);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedOrderStatus.isEmpty) {
        _selectedOrderStatus = [DerivativesOrderStatusFilterEnum.all];
      }
      // If all statuses are selected (excluding 'all'), switch to 'all'
      if (_selectedOrderStatus.length ==
          DerivativesOrderStatusFilterEnum.values.length - 1) {
        _selectedOrderStatus = [DerivativesOrderStatusFilterEnum.all];
      }
    });
  }

  void _onReset() {
    setState(() {
      _selectedTransactionType = OrderTypeEnum.all;
      _selectedOrderStatus = [DerivativesOrderStatusFilterEnum.all];
    });
  }

  void _onApply() {
    if (widget.onApply != null) {
      List<DerivativesOrderStatusFilterEnum> statusToReturn;
      if (_selectedOrderStatus.contains(DerivativesOrderStatusFilterEnum.all)) {
        statusToReturn = [DerivativesOrderStatusFilterEnum.all];
      } else {
        statusToReturn = List<DerivativesOrderStatusFilterEnum>.from(
          _selectedOrderStatus,
        );
      }

      final filterParam = DerivativesFilterParam(
        transactionType: _selectedTransactionType,
        orderStatus: statusToReturn,
      );

      widget.onApply!(filterParam);
    }
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: SizedBox(
        width: MediaQuery.sizeOf(context).width,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              VPTradingLocalize.current.trading_transaction_type,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 16,
              children: [
                ...OrderTypeEnum.values.map(
                  (e) => VPChipView.dynamic(
                    text: e.titleOrderDerivative,
                    size: ChipSize.medium,
                    onTap: () => _onSelectTransactionType(e),
                    style:
                        _selectedTransactionType == e
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              VPTradingLocalize.current.trading_status_filter_title,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 16,
              children: [
                VPChipView.dynamic(
                  text: VPTradingLocalize.current.trading_all,
                  size: ChipSize.medium,
                  onTap: _onTapAllOrderStatus,
                  style:
                      isSelectAllOrderStatus
                          ? ChipStyle.selected
                          : ChipStyle.chipDefault,
                ),
                ...DerivativesOrderStatusFilterEnum.values
                    .where((e) => e != DerivativesOrderStatusFilterEnum.all)
                    .map(
                      (e) => VPChipView.dynamic(
                        text: e.title,
                        size: ChipSize.medium,
                        onTap: () => _onTapOrderStatus(e),
                        style:
                            isSelectAllOrderStatus
                                ? ChipStyle.selected
                                : _selectedOrderStatus.contains(e)
                                ? ChipStyle.selected
                                : ChipStyle.chipDefault,
                      ),
                    ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: VpsButton.secondaryXsSmall(
                    title: VPTradingLocalize.current.trading_reset,
                    onPressed: _onReset,
                    alignment: Alignment.center,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: VpsButton.primaryXsSmall(
                    title: VPTradingLocalize.current.trading_apply,
                    onPressed: _onApply,
                    alignment: Alignment.center,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
