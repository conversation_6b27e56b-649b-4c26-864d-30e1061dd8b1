import 'package:flutter_test/flutter_test.dart';
import 'package:vp_trading/cubit/derivatives_order_book_filter/derivatives_order_book_filter_cubit.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/screen/derivatives_order_book/enum/derivatives_order_status_filter_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void main() {
  group('DerivativesOrderBookFilterCubit', () {
    late DerivativesOrderBookFilterCubit cubit;

    setUp(() {
      cubit = DerivativesOrderBookFilterCubit();
    });

    tearDown(() {
      cubit.close();
    });

    test('should have initial state with default values', () {
      expect(cubit.state.currentTabIndex, equals(0));
      expect(cubit.state.regularOrderFilter, isNull);
      expect(cubit.state.conditionalOrderFilter, isNull);
    });

    test('should initialize with default filter values', () {
      cubit.init();

      expect(cubit.state.regularOrderFilter, isNotNull);
      expect(cubit.state.conditionalOrderFilter, isNotNull);
      expect(cubit.state.regularOrderFilter!.transactionType, equals(OrderTypeEnum.all));
      expect(cubit.state.regularOrderFilter!.orderStatus, contains(DerivativesOrderStatusFilterEnum.all));
    });

    test('should change tab index', () {
      cubit.init();
      cubit.changeTab(1);

      expect(cubit.state.currentTabIndex, equals(1));
    });

    test('should update regular order filter', () {
      cubit.init();
      
      final newFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [DerivativesOrderStatusFilterEnum.pendingSend],
      );

      cubit.updateRegularOrderFilter(newFilter);

      expect(cubit.state.regularOrderFilter!.transactionType, equals(OrderTypeEnum.buy));
      expect(cubit.state.regularOrderFilter!.orderStatus, contains(DerivativesOrderStatusFilterEnum.pendingSend));
    });

    test('should update conditional order filter', () {
      cubit.init();
      
      final newFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.sell,
        orderStatus: [DerivativesOrderStatusFilterEnum.fullyFilled],
      );

      cubit.updateConditionalOrderFilter(newFilter);

      expect(cubit.state.conditionalOrderFilter!.transactionType, equals(OrderTypeEnum.sell));
      expect(cubit.state.conditionalOrderFilter!.orderStatus, contains(DerivativesOrderStatusFilterEnum.fullyFilled));
    });

    test('should update current filter based on active tab', () {
      cubit.init();
      
      final newFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [DerivativesOrderStatusFilterEnum.cancelled],
      );

      // Test regular orders tab (index 0)
      cubit.changeTab(0);
      cubit.updateCurrentFilter(newFilter);
      expect(cubit.state.regularOrderFilter!.transactionType, equals(OrderTypeEnum.buy));

      // Test conditional orders tab (index 1)
      cubit.changeTab(1);
      cubit.updateCurrentFilter(newFilter);
      expect(cubit.state.conditionalOrderFilter!.transactionType, equals(OrderTypeEnum.buy));
    });

    test('should reset current filter to default', () {
      cubit.init();
      
      // Apply some filter first
      final customFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [DerivativesOrderStatusFilterEnum.cancelled],
      );
      cubit.updateCurrentFilter(customFilter);

      // Reset filter
      cubit.resetCurrentFilter();

      expect(cubit.state.currentFilter!.transactionType, equals(OrderTypeEnum.all));
      expect(cubit.state.currentFilter!.orderStatus, contains(DerivativesOrderStatusFilterEnum.all));
    });

    test('should reset all filters to default', () {
      cubit.init();
      
      // Apply some filters first
      final customFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [DerivativesOrderStatusFilterEnum.cancelled],
      );
      cubit.updateRegularOrderFilter(customFilter);
      cubit.updateConditionalOrderFilter(customFilter);

      // Reset all filters
      cubit.resetAllFilters();

      expect(cubit.state.regularOrderFilter!.transactionType, equals(OrderTypeEnum.all));
      expect(cubit.state.conditionalOrderFilter!.transactionType, equals(OrderTypeEnum.all));
    });

    test('should return correct filter display text', () {
      cubit.init();

      // Default state should show "Bộ lọc"
      expect(cubit.getCurrentFilterDisplayText(), equals("Bộ lọc"));

      // Apply transaction type filter
      final transactionFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [DerivativesOrderStatusFilterEnum.all],
      );
      cubit.updateCurrentFilter(transactionFilter);
      expect(cubit.getCurrentFilterDisplayText(), contains("Lệnh mua"));

      // Apply status filter
      final statusFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.all,
        orderStatus: [DerivativesOrderStatusFilterEnum.pendingSend],
      );
      cubit.updateCurrentFilter(statusFilter);
      expect(cubit.getCurrentFilterDisplayText(), contains("Chờ Gửi"));
    });
  });
}
