import 'package:flutter_test/flutter_test.dart';
import 'package:vp_trading/model/request/derivatives_order_book/derivatives_filter_param.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void main() {
  group('DerivativesFilterParam', () {
    test('should create filter param with correct values', () {
      const filter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [OrderStatusEnum.waiting],
      );

      expect(filter.transactionType, OrderTypeEnum.buy);
      expect(filter.orderStatus, contains(OrderStatusEnum.waiting));
    });

    test('should detect default state correctly', () {
      const defaultFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.all,
        orderStatus: [OrderStatusEnum.all],
      );

      expect(defaultFilter.isDefault, isTrue);

      const customFilter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [OrderStatusEnum.waiting],
      );

      expect(customFilter.isDefault, isFalse);
    });

    test('should generate correct status codes for request', () {
      const filter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [OrderStatusEnum.waiting, OrderStatusEnum.matched],
      );

      final statusCodes = filter.statusCodesForRequest;
      expect(statusCodes, isNotEmpty);
      expect(statusCodes, contains('PS')); // From waiting status
      expect(statusCodes, contains('FF')); // From matched status
    });

    test('should return empty string for all status', () {
      const filter = DerivativesFilterParam(
        transactionType: OrderTypeEnum.all,
        orderStatus: [OrderStatusEnum.all],
      );

      expect(filter.statusCodesForRequest, isEmpty);
    });

    test('should copy with new values', () {
      const original = DerivativesFilterParam(
        transactionType: OrderTypeEnum.buy,
        orderStatus: [OrderStatusEnum.waiting],
      );

      final copied = original.copyWith(
        transactionType: OrderTypeEnum.sell,
      );

      expect(copied.transactionType, OrderTypeEnum.sell);
      expect(copied.orderStatus, original.orderStatus);
    });
  });
}
